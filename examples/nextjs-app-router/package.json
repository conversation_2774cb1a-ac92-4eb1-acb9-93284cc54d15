{"name": "nextjs-app-router-example", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@unilab/uniweb3": "^0.0.12", "@unilab/urpc": "^0.0.15", "@unilab/urpc-core": "^0.0.10", "@unilab/urpc-next": "^0.0.15", "next": "^15.0.0", "react": "^19", "react-dom": "^19"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "^15.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}