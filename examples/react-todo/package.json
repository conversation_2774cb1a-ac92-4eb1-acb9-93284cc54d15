{"name": "react-todo-demo", "version": "0.0.1", "description": "React Todo Demo using UniRender and URPC", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@unilab/ukit": "^0.0.11", "@unilab/urpc": "^0.0.15", "@unilab/urpc-adapters": "^0.0.12", "@unilab/urpc-core": "^0.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.6.0", "@tailwindcss/vite": "^4.1.11", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.0.2", "vite": "^7.0.4"}}