{"name": "playground", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "wangler:dev": "wrangler dev", "build": "next build", "start": "next start", "lint": "next lint", "cf:deploy": "wrangler deploy", "pages:build": "opennextjs-cloudflare build", "preview": "wrangler dev", "deploy": "opennextjs-cloudflare deploy -- --keep-vars"}, "dependencies": {"@copilotkit/react-core": "^1.9.3", "@copilotkit/react-ui": "^1.9.3", "@mastra/core": "latest", "@openrouter/ai-sdk-provider": "1.0.0-beta.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/react-syntax-highlighter": "^15.5.13", "@unilab/mastra-client-plugin": "^0.0.8", "@unilab/mastra-plugin": "^0.0.13", "@unilab/urpc": "^0.0.19", "@unilab/urpc-adapters": "^0.0.16", "@unilab/urpc-core": "^0.0.14", "@unilab/urpc-next": "^0.0.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.0", "lucide-react": "^0.525.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "zod": "^3.25.67"}, "devDependencies": {"@opennextjs/cloudflare": "^1.6.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}