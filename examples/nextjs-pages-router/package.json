{"name": "nextjs-pages-router", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@unilab/urpc": "^0.0.15", "@unilab/urpc-core": "^0.0.10", "@unilab/urpc-next": "^0.0.15", "@unilab/uniweb3": "^0.0.12", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}