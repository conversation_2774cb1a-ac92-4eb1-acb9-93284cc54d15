{"name": "@unilab/ukit", "version": "0.0.16", "description": "A flexible and elegant UI rendering library for dynamic data visualization with multiple layout options", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./globals.css": "./dist/globals.css"}, "publishConfig": {"access": "public"}, "files": ["dist", "src"], "scripts": {"build": "vite build", "dev": "vite build --watch", "prepublishOnly": "npm run build", "preview": "vite preview"}, "keywords": ["ui", "render", "table", "form", "dashboard", "data-visualization", "react", "typescript", "shadcn", "radix-ui"], "author": "Unify Team", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0", "tailwindcss": ">=4.0.0"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.6.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "vite": "^7.0.4", "vite-plugin-dts": "^4.5.4"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@unilab/urpc": "^0.0.20", "@unilab/urpc-core": "^0.0.15", "ajv": "^8.17.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.400.0", "tailwind-merge": "^2.4.0"}, "repository": {"type": "git", "url": "https://github.com/unifi-lab/unify.git", "directory": "packages/ukit"}, "bugs": {"url": "https://github.com/unifi-lab/unify/issues"}, "homepage": "https://github.com/unifi-lab/unify#readme"}