{"name": "@unilab/mastra-client-plugin", "version": "0.0.9", "description": "Mastra client plugin for the Unify RPC framework", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./entities": "./dist/entities/index.js"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "bun test", "prepublishOnly": "bun run build"}, "keywords": ["unify", "urpc", "mastra-client", "plugin"], "author": "", "license": "MIT", "dependencies": {"@mastra/core": "^0.10.12", "@openrouter/ai-sdk-provider": "^0.7.2", "@unilab/urpc-core": "^0.0.15", "@unilab/urpc": "^0.0.20"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git", "directory": "packages/mastra-plugin"}}