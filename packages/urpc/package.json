{"name": "@unilab/urpc", "version": "0.0.20", "description": "Unify RPC Framework", "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "prepublishOnly": "bun run build"}, "keywords": ["urpc"], "author": "", "license": "MIT", "dependencies": {"@unilab/urpc-core": "^0.0.15"}, "devDependencies": {"typescript": "^5.3.0"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git"}, "browser": true}