import { Fields } from "@unilab/urpc-core";

export class WeatherEntity {
  static displayName = "WeatherEntity";

  @Fields.string({
    description: "Location to get weather for (city name or coordinates)",
  })
  location = "";

  @Fields.string({
    description: "Temperature unit (f for Fahrenheit, c for Celsius)",
  })
  unit?: string = "f";

  @Fields.string({
    description: "Response format (json or xml)",
  })
  format?: string = "json";

  // Location information
  @Fields.string({
    description: "City name",
  })
  city = "";

  @Fields.string({
    description: "Region/State",
  })
  region = "";

  @Fields.string({
    description: "Country",
  })
  country = "";

  @Fields.string({
    description: "Latitude",
  })
  lat = "";

  @Fields.string({
    description: "Longitude",
  })
  long = "";

  @Fields.string({
    description: "Timezone ID",
  })
  timezone_id = "";

  // Current weather observation
  @Fields.number({
    description: "Publication date (timestamp)",
  })
  pubDate = 0;

  @Fields.number({
    description: "Current temperature",
  })
  temperature = 0;

  @Fields.string({
    description: "Weather condition text",
  })
  condition_text = "";

  @Fields.number({
    description: "Weather condition code",
  })
  condition_code = 0;

  // Wind information
  @Fields.number({
    description: "Wind chill temperature",
  })
  wind_chill = 0;

  @Fields.string({
    description: "Wind direction",
  })
  wind_direction = "";

  @Fields.number({
    description: "Wind speed",
  })
  wind_speed = 0;

  // Atmosphere information
  @Fields.number({
    description: "Humidity percentage",
  })
  humidity = 0;

  @Fields.number({
    description: "Visibility in miles",
  })
  visibility = 0;

  @Fields.number({
    description: "Atmospheric pressure",
  })
  pressure = 0;

  // Astronomy information
  @Fields.string({
    description: "Sunrise time",
  })
  sunrise = "";

  @Fields.string({
    description: "Sunset time",
  })
  sunset = "";
}
