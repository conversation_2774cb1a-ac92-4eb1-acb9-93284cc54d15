import { Fields } from "@unilab/urpc-core";

export class ForecastEntity {
  static displayName = "ForecastEntity";

  @Fields.string({
    description: "Location to get forecast for",
  })
  location = "";

  @Fields.string({
    description: "Temperature unit (f for Fahrenheit, c for Celsius)",
  })
  unit?: string = "f";

  @Fields.number({
    description: "Number of forecast days to return",
  })
  days?: number = 7;

  // Forecast day information
  @Fields.string({
    description: "Day of the week",
  })
  day = "";

  @Fields.number({
    description: "Date timestamp",
  })
  date = 0;

  @Fields.number({
    description: "High temperature",
  })
  high = 0;

  @Fields.number({
    description: "Low temperature",
  })
  low = 0;

  @Fields.string({
    description: "Weather condition text",
  })
  text = "";

  @Fields.number({
    description: "Weather condition code",
  })
  code = 0;
}
