import {
  BaseAdapter,
  ErrorCodes,
  FindOneArgs,
  FindManyArgs,
  URPCError,
} from "@unilab/urpc-core";
import { WeatherEntity } from "../entities/weather";
import { ForecastEntity } from "../entities/forecast";

export class <PERSON><PERSON>eatherAdapter extends BaseAdapter<WeatherEntity> {
  static displayName = "YahooWeatherAdapter";

  private readonly baseUrl = "https://yahoo-weather5.p.rapidapi.com/weather";
  private readonly rapidApiHost = "yahoo-weather5.p.rapidapi.com";

  private getHeaders(): Record<string, string> {
    const apiKey = process.env.RAPID_API_KEY;
    if (!apiKey) {
      throw new URPCError(ErrorCodes.BAD_REQUEST, "RAPID_API_KEY is not set");
    }

    return {
      "x-rapidapi-host": this.rapidApiHost,
      "x-rapidapi-key": apiKey,
    };
  }

  async findOne(args: FindOneArgs<WeatherEntity>): Promise<WeatherEntity | null> {
    const { location, unit = "f", format = "json" } = args.where;

    if (!location) {
      throw new URPCError(ErrorCodes.BAD_REQUEST, "location is required");
    }

    try {
      const url = `${this.baseUrl}?location=${encodeURIComponent(location)}&format=${format}&u=${unit}`;
      
      const response = await fetch(url, {
        method: "GET",
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new URPCError(
          ErrorCodes.INTERNAL_SERVER_ERROR,
          `Yahoo Weather API error: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.location || !data.current_observation) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Weather data not found for the specified location");
      }

      // Map the API response to our WeatherEntity
      const weather: WeatherEntity = {
        location,
        unit,
        format,
        // Location information
        city: data.location.city || "",
        region: data.location.region || "",
        country: data.location.country || "",
        lat: data.location.lat || "",
        long: data.location.long || "",
        timezone_id: data.location.timezone_id || "",
        // Current observation
        pubDate: data.current_observation.pubDate || 0,
        temperature: data.current_observation.condition?.temperature || 0,
        condition_text: data.current_observation.condition?.text || "",
        condition_code: data.current_observation.condition?.code || 0,
        // Wind information
        wind_chill: data.current_observation.wind?.chill || 0,
        wind_direction: data.current_observation.wind?.direction || "",
        wind_speed: data.current_observation.wind?.speed || 0,
        // Atmosphere information
        humidity: data.current_observation.atmosphere?.humidity || 0,
        visibility: data.current_observation.atmosphere?.visibility || 0,
        pressure: data.current_observation.atmosphere?.pressure || 0,
        // Astronomy information
        sunrise: data.current_observation.astronomy?.sunrise || "",
        sunset: data.current_observation.astronomy?.sunset || "",
      };

      return weather;
    } catch (error) {
      if (error instanceof URPCError) {
        throw error;
      }
      throw new URPCError(
        ErrorCodes.INTERNAL_SERVER_ERROR,
        `Failed to fetch weather data: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async findForecasts(args: FindOneArgs<WeatherEntity>): Promise<ForecastEntity[]> {
    const { location, unit = "f", format = "json" } = args.where;

    if (!location) {
      throw new URPCError(ErrorCodes.BAD_REQUEST, "location is required");
    }

    try {
      const url = `${this.baseUrl}?location=${encodeURIComponent(location)}&format=${format}&u=${unit}`;
      
      const response = await fetch(url, {
        method: "GET",
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new URPCError(
          ErrorCodes.INTERNAL_SERVER_ERROR,
          `Yahoo Weather API error: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.forecasts || !Array.isArray(data.forecasts)) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Forecast data not found for the specified location");
      }

      // Map the forecasts to our ForecastEntity
      const forecasts: ForecastEntity[] = data.forecasts.map((forecast: any) => ({
        location,
        unit,
        day: forecast.day || "",
        date: forecast.date || 0,
        high: forecast.high || 0,
        low: forecast.low || 0,
        text: forecast.text || "",
        code: forecast.code || 0,
      }));

      return forecasts;
    } catch (error) {
      if (error instanceof URPCError) {
        throw error;
      }
      throw new URPCError(
        ErrorCodes.INTERNAL_SERVER_ERROR,
        `Failed to fetch forecast data: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }
}
