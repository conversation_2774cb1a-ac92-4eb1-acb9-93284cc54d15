import { Plugin } from "@unilab/urpc-core";
import { WeatherEntity } from "./entities/weather";
import { ForecastEntity } from "./entities/forecast";
import { YahooWeatherAdapter } from "./adapters/yahoo-weather";

export const WeatherPlugin = (): Plugin => {
  return {
    entities: [WeatherEntity, ForecastEntity],
    adapters: [
      {
        source: "yahoo-weather",
        entity: "weather",
        adapter: new YahooWeatherAdapter(),
      },
      {
        source: "yahoo-weather",
        entity: "forecast",
        adapter: new YahooWeatherAdapter(),
      },
    ],
  };
};

// Export entities for external use
export * from "./entities";
export * from "./adapters";
