{"name": "@unilab/weather", "version": "0.0.1", "description": "Weather plugin for UniLab", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./entities": {"import": "./dist/entities/index.js", "require": "./dist/entities/index.js"}}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "prepublishOnly": "bun run build"}, "keywords": ["weather", "plugin", "yahoo-weather", "unify"], "dependencies": {"@unilab/urpc-core": "^0.0.15"}, "devDependencies": {"typescript": "^5.6.2"}, "peerDependencies": {}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git"}, "license": "MIT"}