{"name": "@unilab/uniweb3", "version": "0.0.18", "description": "Unified source plugins for blockchain data", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./entities": {"import": "./dist/entities/index.js", "require": "./dist/entities/index.js"}}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "prepublishOnly": "bun run build"}, "keywords": ["blockchain", "solana", "evm", "ethereum", "plugin"], "dependencies": {"@solana/web3.js": "^1.95.2", "@unilab/urpc-core": "^0.0.15", "@unilab/urpc-adapters": "^0.0.17", "reflect-metadata": "^0.2.2", "viem": "^2.33.0"}, "devDependencies": {"typescript": "^5.6.2"}, "peerDependencies": {}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git"}, "license": "MIT"}