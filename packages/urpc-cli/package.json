{"name": "@unilab/urpc-cli", "version": "0.0.8", "description": "CLI tool for creating URPC projects", "main": "dist/index.cjs", "publishConfig": {"access": "public"}, "bin": {"urpc-cli": "bin/urpc-cli.js"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "prepublishOnly": "bun run build", "test": "bun run ./bin/urpc-cli.js create myprojet"}, "files": ["dist", "bin", "templates"], "dependencies": {"@unilab/urpc": "^0.0.7", "boxen": "^7.1.1", "chalk": "^5.3.0", "chokidar": "^3.5.3", "commander": "^11.1.0", "cross-spawn": "^7.0.3", "detect-port": "^1.5.1", "figlet": "^1.6.0", "fs-extra": "^11.2.0", "gradient-string": "^2.0.2", "inquirer": "^9.2.12", "node-fetch": "^3.3.2", "open": "^9.1.0", "ora": "^7.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/figlet": "^1.5.8", "@types/cross-spawn": "^6.0.6", "@types/gradient-string": "^1.1.5", "@types/detect-port": "^1.3.5", "@types/node-fetch": "^2.6.9", "typescript": "^5.3.2", "tsup": "^8.0.0"}, "keywords": ["urpc", "cli", "template", "generator"], "author": "Unify Team", "license": "MIT"}