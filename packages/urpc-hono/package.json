{"name": "@unilab/urpc-hono", "version": "0.0.18", "description": "URPC Hono Adapter", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "prepublishOnly": "bun run build"}, "keywords": ["hono", "urpc"], "author": "", "license": "MIT", "dependencies": {"@unilab/urpc-core": "^0.0.15", "hono": "^4.7.11", "@hono/node-server": "^1.12.2"}, "devDependencies": {"typescript": "^5.3.0", "@types/bun": "^1.1.12"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git"}}