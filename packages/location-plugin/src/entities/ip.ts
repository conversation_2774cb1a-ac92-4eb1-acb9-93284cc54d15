import { Fields } from "@unilab/urpc-core";

export class IPEntity {
  static displayName = "IPEntity";

  @Fields.string({
    description: "The IP address to get information for (optional, will auto-detect if not provided)",
  })
  ip?: string;

  @Fields.string({
    description: "Response format (json or text)",
  })
  format?: string = "json";

  @Fields.string({
    description: "The detected or provided IP address",
  })
  address = "";
}
