import { Fields } from "@unilab/urpc-core";

export class LocationEntity {
  static displayName = "LocationEntity";

  @Fields.string({
    description: "The IP address to get location for (optional, will auto-detect if not provided)",
  })
  ip?: string;

  @Fields.string({
    description: "Include additional details (hostname, org, etc.)",
  })
  details?: string = "basic";

  // Basic location information
  @Fields.string({
    description: "IP address",
  })
  address = "";

  @Fields.string({
    description: "Hostname associated with the IP",
  })
  hostname = "";

  @Fields.string({
    description: "City name",
  })
  city = "";

  @Fields.string({
    description: "Region/State name",
  })
  region = "";

  @Fields.string({
    description: "Country code (2-letter ISO)",
  })
  country = "";

  @Fields.string({
    description: "Coordinates in 'lat,lng' format",
  })
  loc = "";

  @Fields.string({
    description: "Latitude",
  })
  latitude = "";

  @Fields.string({
    description: "Longitude",
  })
  longitude = "";

  @Fields.string({
    description: "Organization/ISP information",
  })
  org = "";

  @Fields.string({
    description: "Postal/ZIP code",
  })
  postal = "";

  @Fields.string({
    description: "Timezone identifier",
  })
  timezone = "";

  @Fields.string({
    description: "Additional readme information",
  })
  readme = "";
}
