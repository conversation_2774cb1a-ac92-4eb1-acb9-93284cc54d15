import { Plugin } from "@unilab/urpc-core";
import { IPEntity } from "./entities/ip";
import { LocationEntity } from "./entities/location";
import { IPifyAdapter } from "./adapters/ipify";
import { IPInfoAdapter } from "./adapters/ipinfo";

export const LocationPlugin = (): Plugin => {
  return {
    entities: [IPEntity, LocationEntity],
    adapters: [
      {
        source: "ipify",
        entity: "ip",
        adapter: new IPifyAdapter(),
      },
      {
        source: "ipinfo",
        entity: "location",
        adapter: new IPInfoAdapter(),
      },
    ],
  };
};

// Export entities for external use
export * from "./entities";
export * from "./adapters";
