import {
  Base<PERSON><PERSON>pter,
  <PERSON>rror<PERSON><PERSON>,
  Find<PERSON>neArgs,
  URPCError,
} from "@unilab/urpc-core";
import { IPEntity } from "../entities/ip";

export class IPifyAdapter extends BaseAdapter<IPEntity> {
  static displayName = "IPifyAdapter";

  private readonly baseUrl = "https://api.ipify.org";

  async findOne(args?: FindOneArgs<IPEntity>): Promise<IPEntity | null> {
    const format = args?.where?.format || "json";

    try {
      const url = `${this.baseUrl}?format=${format}`;
      
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Accept": "application/json",
          "User-Agent": "UniLab-Location-Plugin/1.0",
        },
      });

      if (!response.ok) {
        throw new URPCError(
          ErrorCodes.INTERNAL_SERVER_ERROR,
          `IPify API error: ${response.status} ${response.statusText}`
        );
      }

      if (format === "json") {
        const data = await response.json();
        
        if (!data.ip) {
          throw new URPCError(ErrorCodes.NOT_FOUND, "IP address not found in response");
        }

        return {
          ip: data.ip,
          format,
          address: data.ip,
        };
      } else {
        // For text format
        const ipAddress = await response.text();
        
        if (!ipAddress || ipAddress.trim() === "") {
          throw new URPCError(ErrorCodes.NOT_FOUND, "IP address not found in response");
        }

        return {
          format,
          address: ipAddress.trim(),
        };
      }
    } catch (error) {
      if (error instanceof URPCError) {
        throw error;
      }
      throw new URPCError(
        ErrorCodes.INTERNAL_SERVER_ERROR,
        `Failed to fetch IP address: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async getCurrentIP(): Promise<string> {
    try {
      const result = await this.findOne({ where: { format: "json" } });
      return result?.address || "";
    } catch (error) {
      throw new URPCError(
        ErrorCodes.INTERNAL_SERVER_ERROR,
        `Failed to get current IP: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }
}
