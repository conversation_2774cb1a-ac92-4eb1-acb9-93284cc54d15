import {
  BaseAdapter,
  Error<PERSON>odes,
  FindOneArgs,
  URPCError,
} from "@unilab/urpc-core";
import { LocationEntity } from "../entities/location";

export class IPInfoAdapter extends BaseAdapter<LocationEntity> {
  static displayName = "IPInfoAdapter";

  private readonly baseUrl = "https://ipinfo.io";

  async findOne(args?: FindOneArgs<LocationEntity>): Promise<LocationEntity | null> {
    const ip = args?.where?.ip;
    const details = args?.where?.details || "basic";

    try {
      // If no IP is provided, get current IP location
      const url = ip ? `${this.baseUrl}/${ip}` : this.baseUrl;
      
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Accept": "application/json",
          "User-Agent": "UniLab-Location-Plugin/1.0",
        },
      });

      if (!response.ok) {
        throw new URPCError(
          ErrorCodes.INTERNAL_SERVER_ERROR,
          `IPInfo API error: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.ip) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Location data not found for the specified IP");
      }

      // Parse coordinates if available
      let latitude = "";
      let longitude = "";
      if (data.loc) {
        const coords = data.loc.split(",");
        if (coords.length === 2) {
          latitude = coords[0].trim();
          longitude = coords[1].trim();
        }
      }

      // Map the API response to our LocationEntity
      const location: LocationEntity = {
        ip,
        details,
        address: data.ip || "",
        hostname: data.hostname || "",
        city: data.city || "",
        region: data.region || "",
        country: data.country || "",
        loc: data.loc || "",
        latitude,
        longitude,
        org: data.org || "",
        postal: data.postal || "",
        timezone: data.timezone || "",
        readme: data.readme || "",
      };

      return location;
    } catch (error) {
      if (error instanceof URPCError) {
        throw error;
      }
      throw new URPCError(
        ErrorCodes.INTERNAL_SERVER_ERROR,
        `Failed to fetch location data: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async getLocationByIP(ipAddress: string): Promise<LocationEntity | null> {
    return this.findOne({
      where: {
        ip: ipAddress,
        details: "full",
      },
    });
  }

  async getCurrentLocation(): Promise<LocationEntity | null> {
    return this.findOne({
      where: {
        details: "full",
      },
    });
  }
}
