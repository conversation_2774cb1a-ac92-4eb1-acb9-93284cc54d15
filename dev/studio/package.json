{"name": "urpc-studio", "version": "0.0.1", "type": "module", "description": "Lightweight browser-only client library for URPC without HTTP functionality", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"urpc-studio": "bin/urpc-studio.js"}, "scripts": {"studio": "vite", "studio:build": "vite build", "studio:preview": "vite preview", "build": "vite build", "dev": "vite", "preview": "vite preview", "prepublishOnly": "npm run build", "pages:build": "vite build", "deploy": "npm run pages:build && npx wrangler pages deploy dist --project-name=unify-studio", "cf:deploy": "npm run pages:build && npx wrangler pages deploy dist --project-name=unify-studio"}, "keywords": ["api", "client", "typescript", "browser", "type-safe", "lightweight", "studio", "unify", "development-tool"], "author": "Unify Team", "license": "MIT", "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@unilab/urpc": "workspace:*", "@unilab/ukit": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "prism-react-renderer": "^2.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-tree": "^0.20.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.57"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "@tailwindcss/vite": "^4.1.11", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.3.0", "vite": "^7.0.4"}, "files": ["dist", "bin", "index.html", "src", "public", "vite.config.ts", "tailwind.config.js", "tsconfig.json", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git"}, "publishConfig": {"access": "public"}, "preferGlobal": true, "browser": false, "overrides": {"rollup": "4.29.2"}}