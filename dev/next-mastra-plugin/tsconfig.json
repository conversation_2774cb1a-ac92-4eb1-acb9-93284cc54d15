{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "strictNullChecks": true}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "core/**/*.ts", "core/**/*.tsx", "entities/**/*.ts", "entities/**/*.tsx", "app/**/*.ts", "app/**/*.tsx"], "exclude": ["node_modules"]}