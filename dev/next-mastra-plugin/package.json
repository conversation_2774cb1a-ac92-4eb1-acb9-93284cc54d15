{"name": "next-mastra-plugin", "version": "1.0.0", "description": "<PERSON><PERSON> Plug<PERSON>", "main": "server.ts", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "bunx tsx test.ts"}, "dependencies": {"@mastra/core": "^0.10.8", "@openrouter/ai-sdk-provider": "^0.7.2", "@tailwindcss/postcss": "^4.1.11", "@unilab/mastra-client-plugin": "workspace:*", "@unilab/mastra-plugin": "workspace:*", "@unilab/ukit": "workspace:*", "@unilab/uniweb3": "workspace:*", "@unilab/urpc": "workspace:*", "@unilab/urpc-adapters": "workspace:*", "@unilab/urpc-core": "workspace:*", "@unilab/urpc-next": "workspace:*", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "dotenv": "^17.0.0", "lucide-react": "^0.294.0", "next": "^14.0.0", "next-themes": "^0.4.6", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "zod": "^3.25.67"}, "devDependencies": {"@types/bun": "^1.1.12", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.8.3", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}, "keywords": ["nextjs", "mastra", "urpc", "agent", "ai", "crud", "demo"], "author": "", "license": "MIT"}