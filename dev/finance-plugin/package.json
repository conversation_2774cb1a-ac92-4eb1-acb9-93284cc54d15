{"name": "finance-plugin", "version": "1.0.0", "description": "Finance plugin for @unilab/urpc", "main": "server.ts", "scripts": {"server": "bun run server.ts", "mock": "bun run mock.ts", "memory": "bun run memory.ts", "indexeddb": "VITE_ENTRY=indexeddb.html vite --port 3000", "localstorage": "VITE_ENTRY=localstorage.html vite --port 3000"}, "dependencies": {"@unilab/finance": "workspace:*", "@unilab/urpc": "workspace:*", "@unilab/urpc-adapters": "workspace:*", "@unilab/urpc-core": "workspace:*", "@unilab/urpc-hono": "workspace:*"}, "devDependencies": {"typescript": "^5.3.0", "vite": "^7.0.4"}, "keywords": ["unilab", "browser", "example"], "author": "", "license": "MIT"}