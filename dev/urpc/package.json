{"name": "browser-example", "version": "1.0.0", "description": "Basic browser example using @unilab/urpc", "main": "server.ts", "scripts": {"server": "bun run server.ts", "mock": "bun run mock.ts", "memory": "bun run memory.ts", "indexeddb": "VITE_ENTRY=indexeddb.html vite --port 3000", "localstorage": "VITE_ENTRY=localstorage.html vite --port 3000"}, "dependencies": {"@unilab/uniweb3": "workspace:*", "@unilab/urpc": "workspace:*", "@unilab/urpc-adapters": "workspace:*", "@unilab/urpc-core": "workspace:*", "@unilab/urpc-hono": "workspace:*", "viem": "^2.33.0"}, "devDependencies": {"typescript": "^5.3.0", "vite": "^7.0.4"}, "keywords": ["unilab", "browser", "example"], "author": "", "license": "MIT"}