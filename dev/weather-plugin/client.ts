import { repo, URPC } from "@unilab/urpc";
import { WeatherEntity, ForecastEntity } from "@unilab/weather/entities";

// Initialize URPC client
URPC.init({
  baseUrl: "http://localhost:3000",
  timeout: 10000,
});

async function testWeatherQueries() {
  console.log("🌤️  Testing Weather Plugin...\n");

  try {
    // Test 1: Get current weather for Sunnyvale
    console.log("📍 Test 1: Getting current weather for Sunnyvale...");
    const sunnyvaleWeather = await repo<WeatherEntity>({
      entity: "weather",
    }).findOne({
      where: {
        location: "sunnyvale",
        unit: "f",
        format: "json",
      },
    });

    if (sunnyvaleWeather) {
      console.log("✅ Sunnyvale Weather:");
      console.log(`   🏙️  Location: ${sunnyvaleWeather.city}, ${sunnyvaleWeather.region}, ${sunnyvaleWeather.country}`);
      console.log(`   🌡️  Temperature: ${sunnyvaleWeather.temperature}°F`);
      console.log(`   ☁️  Condition: ${sunnyvaleWeather.condition_text}`);
      console.log(`   💨 Wind: ${sunnyvaleWeather.wind_direction} ${sunnyvaleWeather.wind_speed} mph`);
      console.log(`   💧 Humidity: ${sunnyvaleWeather.humidity}%`);
      console.log(`   🌅 Sunrise: ${sunnyvaleWeather.sunrise}`);
      console.log(`   🌇 Sunset: ${sunnyvaleWeather.sunset}\n`);
    } else {
      console.log("❌ No weather data found for Sunnyvale\n");
    }

    // Test 2: Get weather forecasts for Sunnyvale
    console.log("📍 Test 2: Getting weather forecasts for Sunnyvale...");
    const sunnyvaleForecasts = await repo<WeatherEntity>({
      entity: "weather",
    }).findForecasts({
      where: {
        location: "sunnyvale",
        unit: "f",
        format: "json",
      },
    });

    if (sunnyvaleForecasts && sunnyvaleForecasts.length > 0) {
      console.log("✅ Sunnyvale 7-Day Forecast:");
      sunnyvaleForecasts.slice(0, 7).forEach((forecast: any, index: number) => {
        console.log(`   ${index + 1}. ${forecast.day}: ${forecast.text}, High: ${forecast.high}°F, Low: ${forecast.low}°F`);
      });
      console.log("");
    } else {
      console.log("❌ No forecast data found for Sunnyvale\n");
    }

    // Test 3: Get weather for different location (New York)
    console.log("📍 Test 3: Getting current weather for New York...");
    const nyWeather = await repo<WeatherEntity>({
      entity: "weather",
    }).findOne({
      where: {
        location: "new york",
        unit: "f",
        format: "json",
      },
    });

    if (nyWeather) {
      console.log("✅ New York Weather:");
      console.log(`   🏙️  Location: ${nyWeather.city}, ${nyWeather.region}, ${nyWeather.country}`);
      console.log(`   🌡️  Temperature: ${nyWeather.temperature}°F`);
      console.log(`   ☁️  Condition: ${nyWeather.condition_text}`);
      console.log(`   💨 Wind: ${nyWeather.wind_direction} ${nyWeather.wind_speed} mph`);
      console.log(`   💧 Humidity: ${nyWeather.humidity}%\n`);
    } else {
      console.log("❌ No weather data found for New York\n");
    }

    // Test 4: Get weather in Celsius
    console.log("📍 Test 4: Getting weather for London in Celsius...");
    const londonWeather = await repo<WeatherEntity>({
      entity: "weather",
    }).findOne({
      where: {
        location: "london",
        unit: "c",
        format: "json",
      },
    });

    if (londonWeather) {
      console.log("✅ London Weather (Celsius):");
      console.log(`   🏙️  Location: ${londonWeather.city}, ${londonWeather.region}, ${londonWeather.country}`);
      console.log(`   🌡️  Temperature: ${londonWeather.temperature}°C`);
      console.log(`   ☁️  Condition: ${londonWeather.condition_text}`);
      console.log(`   💨 Wind: ${londonWeather.wind_direction} ${londonWeather.wind_speed} mph`);
      console.log(`   💧 Humidity: ${londonWeather.humidity}%\n`);
    } else {
      console.log("❌ No weather data found for London\n");
    }

    console.log("🎉 All weather tests completed!");

  } catch (error) {
    console.error("❌ Error testing weather queries:", error);
    
    if (error instanceof Error && error.message.includes("RAPID_API_KEY")) {
      console.log("\n💡 Setup Instructions:");
      console.log("1. Get a free API key from: https://rapidapi.com/apishub/api/yahoo-weather5");
      console.log("2. Set the environment variable: export RAPID_API_KEY=your_api_key_here");
      console.log("3. Restart the server with: bun run server");
    }
  }
}

// Run the tests
testWeatherQueries();
