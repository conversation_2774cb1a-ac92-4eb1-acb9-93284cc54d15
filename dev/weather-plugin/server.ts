import { URPC } from "@unilab/urpc-hono";
import { WeatherPlugin } from "@unilab/weather";
import { Hono } from "hono";
import { cors } from "hono/cors";

const app = new Hono();

// Enable CORS for client-side requests
app.use(cors());

// Initialize URPC with Weather Plugin
const urpcApp = URPC.init({
  plugins: [WeatherPlugin()],
  app,
  entityConfigs: {
    weather: {
      defaultSource: "yahoo-weather",
    },
    forecast: {
      defaultSource: "yahoo-weather",
    },
  },
});

console.log("🌤️  Weather Plugin Server starting...");
console.log("📍 Make sure to set RAPID_API_KEY environment variable");
console.log("🔑 Get your API key from: https://rapidapi.com/apishub/api/yahoo-weather5");

const server = {
  port: 3000,
  timeout: 30000,
  fetch: app.fetch,
};

console.log(`🚀 Weather server running on http://localhost:${server.port}`);
console.log("📖 Available endpoints:");
console.log("   GET /api/weather/findOne - Get current weather");
console.log("   GET /api/weather/findForecasts - Get weather forecasts");

export default server;
