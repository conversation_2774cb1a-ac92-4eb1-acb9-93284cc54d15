{"name": "weather-plugin-dev", "version": "1.0.0", "description": "Weather plugin development and testing environment", "main": "server.ts", "scripts": {"server": "bun run server.ts", "client": "bun run client.ts", "dev": "bun run server.ts"}, "dependencies": {"@unilab/weather": "workspace:*", "@unilab/urpc": "workspace:*", "@unilab/urpc-adapters": "workspace:*", "@unilab/urpc-core": "workspace:*", "@unilab/urpc-hono": "workspace:*"}, "devDependencies": {"typescript": "^5.3.0"}, "keywords": ["weather", "yahoo-weather", "unify", "plugin", "development"], "author": "", "license": "MIT"}