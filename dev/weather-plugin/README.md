# Weather Plugin Development Environment

This is a development and testing environment for the Weather Plugin, which integrates Yahoo Weather API with the Unify Protocol.

## 🌤️ Features

- **Current Weather**: Get real-time weather data for any location
- **Weather Forecasts**: Get 7-day weather forecasts
- **Multiple Units**: Support for Fahrenheit and Celsius
- **Rich Data**: Temperature, humidity, wind, pressure, sunrise/sunset times
- **Location Info**: City, region, country, coordinates, timezone

## 🚀 Quick Start

### 1. Get API Key

1. Visit [RapidAPI Yahoo Weather](https://rapidapi.com/apishub/api/yahoo-weather5)
2. Subscribe to the free plan
3. Copy your API key

### 2. Set Environment Variable

```bash
export RAPID_API_KEY=your_api_key_here
```

### 3. Start the Server

```bash
bun run server
```

The server will start on `http://localhost:3000`

### 4. Run Client Tests

In another terminal:

```bash
bun run client
```

## 📖 API Usage Examples

### Get Current Weather

```typescript
import { repo } from "@unilab/urpc";
import { WeatherEntity } from "@unilab/weather/entities";

const weather = await repo<WeatherEntity>({
  entity: "weather",
}).findOne({
  where: {
    location: "sunnyvale",
    unit: "f",        // "f" for Fahrenheit, "c" for Celsius
    format: "json",
  },
});

console.log(`Temperature: ${weather.temperature}°F`);
console.log(`Condition: ${weather.condition_text}`);
```

### Get Weather Forecasts

```typescript
const forecasts = await repo<WeatherEntity>({
  entity: "weather",
}).findForecasts({
  where: {
    location: "new york",
    unit: "c",
  },
});

forecasts.forEach(forecast => {
  console.log(`${forecast.day}: ${forecast.text}, High: ${forecast.high}°C`);
});
```

## 🌍 Supported Locations

The API supports various location formats:
- City names: `"sunnyvale"`, `"new york"`, `"london"`
- City, State: `"san francisco, ca"`
- City, Country: `"paris, france"`
- Coordinates: `"37.4419,-122.1430"`

## 📊 Available Data

### WeatherEntity Fields

- **Location Info**: city, region, country, lat, long, timezone_id
- **Current Conditions**: temperature, condition_text, condition_code
- **Wind**: wind_chill, wind_direction, wind_speed
- **Atmosphere**: humidity, visibility, pressure
- **Astronomy**: sunrise, sunset
- **Metadata**: pubDate

### ForecastEntity Fields

- **Date Info**: day, date
- **Temperature**: high, low
- **Conditions**: text, code

## 🔧 Configuration

The plugin is configured in `server.ts`:

```typescript
URPC.init({
  plugins: [WeatherPlugin()],
  entityConfigs: {
    weather: {
      defaultSource: "yahoo-weather",
    },
    forecast: {
      defaultSource: "yahoo-weather",
    },
  },
});
```

## 🧪 Testing

The client includes comprehensive tests for:
- ✅ Current weather queries
- ✅ Forecast queries  
- ✅ Different locations
- ✅ Temperature units (F/C)
- ✅ Error handling

## 🚨 Troubleshooting

### "RAPID_API_KEY is not set" Error

Make sure you've set the environment variable:
```bash
export RAPID_API_KEY=your_actual_api_key
```

### API Rate Limits

The free tier has rate limits. If you hit them:
- Wait a few minutes before retrying
- Consider upgrading to a paid plan for higher limits

### Location Not Found

Try different location formats:
- Use common city names
- Include state/country for disambiguation
- Check spelling

## 📚 Integration

To use this plugin in your own project:

```bash
npm install @unilab/weather
```

```typescript
import { WeatherPlugin } from "@unilab/weather";
import { URPC } from "@unilab/urpc-hono";

URPC.init({
  plugins: [WeatherPlugin()],
  // ... your config
});
```
