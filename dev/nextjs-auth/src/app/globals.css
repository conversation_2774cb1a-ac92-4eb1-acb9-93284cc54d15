@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(147, 51, 234, 0.7);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Enhanced Dark mode gradient backgrounds */
.dark-gradient-bg {
  background: linear-gradient(135deg, #0f0f23 0%, #1e1b4b 25%, #312e81 50%, #1e40af 75%, #0369a1 100%);
  position: relative;
}

.dark-gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at top, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(ellipse at bottom right, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.dark-card-bg {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(71, 85, 105, 0.4);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.dark-card-bg:hover {
  background: rgba(15, 23, 42, 0.98);
  border: 1px solid rgba(71, 85, 105, 0.6);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 0 20px rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
}

/* Improved button styles */
.gradient-button {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  position: relative;
  overflow: hidden;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button:hover::before {
  left: 100%;
}

/* Enhanced code block styling */
.code-block-dark {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border: 1px solid rgba(71, 85, 105, 0.3);
  position: relative;
}

.code-block-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

/* Particle effect for background */
.particle-bg {
  position: relative;
  overflow: hidden;
}

.particle-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(59, 130, 246, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(147, 51, 234, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(34, 197, 94, 0.3), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(59, 130, 246, 0.3), transparent);
  background-size: 150px 150px;
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}
