{"name": "@unilab-monorepo", "version": "1.0.0", "private": true, "workspaces": ["packages/*", "!packages/urpc-cli", "dev/*"], "scripts": {"dev": "bun --filter '*' dev", "build": "bun --filter '*' build", "preview": "bun --filter '*' preview", "deploy": "bun --filter '*' deploy", "studio:deploy": "cd dev/studio && npm run cf:deploy", "update-examples": "bun scripts/update-examples.ts", "update-examples:workspace": "bun scripts/update-examples.ts --workspace"}, "author": "", "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "^8.35.0"}, "devDependencies": {"vite": "^7.0.4", "@vitejs/plugin-react": "^4.6.0", "@tailwindcss/vite": "^4.1.11"}}