{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev", "start": "next start", "pages:build": "npx @cloudflare/next-on-pages", "preview": "bun run pages:build && wrangler pages dev", "deploy": "bun run pages:build && wrangler pages deploy", "cf:deploy": "bun run pages:build && wrangler pages deploy --project-name=unify-docs --branch=unify-docs", "cf:deploy:only": "npx wrangler pages deploy --project-name=unify-docs --branch=unify-docs", "postinstall": "fumadocs-mdx"}, "dependencies": {"@orama/orama": "^3.1.10", "@shikijs/transformers": "^1.24.2", "@unilab/ukit": "latest", "@unilab/urpc": "latest", "@unilab/urpc-core": "latest", "@unilab/urpc-adapters": "latest", "fumadocs-core": "15.6.1", "fumadocs-docgen": "^2.0.1", "fumadocs-mdx": "11.6.10", "fumadocs-openapi": "^9.0.12", "fumadocs-twoslash": "^3.1.4", "fumadocs-typescript": "^4.0.6", "fumadocs-ui": "15.6.1", "highlight.js": "^11.11.1", "lucide-react": "^0.515.0", "next": "^15.3.5", "react": "^19.1.0", "react-dom": "^19.1.0", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@tailwindcss/postcss": "^4.1.10", "@types/mdx": "^2.0.13", "@types/node": "24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "postcss": "^8.5.5", "storybook": "^8.6.14", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "wrangler": "^4.23.0"}}