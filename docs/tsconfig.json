{"compilerOptions": {"baseUrl": ".", "target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "paths": {"@/.source": ["./.source/index.ts"], "@/*": ["./*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "content/docs/middleware/hook.mdx", "content/docs/middleware/log.mdx", "next-env.d.ts", "out/types/**/*.ts"], "exclude": ["node_modules"]}