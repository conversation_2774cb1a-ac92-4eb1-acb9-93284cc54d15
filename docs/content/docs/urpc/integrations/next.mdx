---
title: Next.js Integration
description: URPC provides complete Next.js support, including both App Router and Pages Router routing methods. This guide will show you how to integrate URPC in Next.js projects.
---

## Install Dependencies

```package-install
@unilab/urpc-next @unilab/urpc @unilab/urpc-core @unilab/uniweb3
```

## App Router Integration

Create API configuration in `src/lib/urpc-api.ts`:

```typescript
import { URPC } from "@unilab/urpc-next/app-router";
import { logging } from "@unilab/urpc-core/middleware";
import { WalletPlugin } from "@unilab/uniweb3";

export const api = URPC.init({
  plugins: [WalletPlugin],
  middlewares: [logging()],
});
```

Create API routes in `src/app/api/[...urpc]/route.ts`:

```typescript
import { api } from "@/lib/urpc-api";

export const { GET, POST } = api;
```

Create urpc client in `src/lib/urpc-client.ts`:

```typescript
import { URPC } from "@unilab/urpc";

export const initURPCClient = () => {
  URPC.init({
    baseUrl: "http://localhost:3000/api",
    timeout: 10000,
  });
}
```

Using URPC client in page components:

```tsx
"use client";

import { initURPCClient } from "@/lib/urpc-client";
import { repo } from "@unilab/urpc";
import { WalletEntity } from "@unilab/uniweb3/entities";
import { useEffect, useState } from "react";

initURPCClient()

export default function Home() {
  const [evmBalanceData, setEvmBalanceData] = useState<any>(null);
  const [solanaBalanceData, setSolanaBalanceData] = useState<any>(null);

  useEffect(() => {
    const fetchEvmBalance = async () => {
      try {
        const data = await repo<WalletEntity>({
          entity: "wallet",
          source: "evm",
        }).findOne({
          where: {
            address: "0x...",
            chainId: 1,
          },
        });
        if (data) {
          setEvmBalanceData(data);
        }
      } catch (error) {
        console.error(error);
      }
    };

    const fetchSolanaBalance = async () => {
      try {
        const data = await repo<WalletEntity>({
          entity: "wallet",
          source: "solana",
        }).findOne({
          where: {
            address: "********************************",
          },
        });
        if (data) {
          setSolanaBalanceData(data);
        }
      } catch (error) {
        console.error(error);
      }
    };

    fetchEvmBalance();
    fetchSolanaBalance();
  }, []);

  return (
    <div className="min-h-screen w-full flex flex-col items-center p-10">
      {evmBalanceData && <p>{JSON.stringify(evmBalanceData, null, 2)}</p>}
      {solanaBalanceData && <p>{JSON.stringify(solanaBalanceData, null, 2)}</p>}
    </div>
  );
}
```

## Pages Router Integration

Create API routes in `src/pages/api/[...urpc].ts`:

```typescript
import { URPC } from "@unilab/urpc-next/pages-router";
import { WalletPlugin } from "@unilab/uniweb3";
import { logging } from "@unilab/urpc-core/middleware";

const handler = URPC.init({
  plugins: [WalletPlugin],
  middlewares: [logging()],
});

export default handler;
```

Using URPC client in page components:

```tsx
import { repo, URPC } from "@unilab/urpc";
import { WalletEntity } from "@unilab/uniweb3/entities";
import { useEffect, useState } from "react";

URPC.init({
  baseUrl: "http://localhost:3000/api",
  timeout: 10000,
});

export default function Home() {
  const [evmBalanceData, setEvmBalanceData] = useState<any>(null);
  const [solanaBalanceData, setSolanaBalanceData] = useState<any>(null);

  useEffect(() => {
    const fetchEvmBalance = async () => {
      try {
        const data = await repo<WalletEntity>({
          entity: "wallet",
          source: "evm",
        }).findOne({
          where: {
            address: "0x...",
            chainId: 1,
          },
        });
        if (data) {
          setEvmBalanceData(data);
        }
      } catch (error) {
        console.error(error);
      }
    };

    const fetchSolanaBalance = async () => {
      try {
        const data = await repo<WalletEntity>({
          entity: "wallet",
          source: "solana",
        }).findOne({
          where: {
            address: "********************************",
          },
        });
        if (data) {
          setSolanaBalanceData(data);
        }
      } catch (error) {
        console.error(error);
      }
    };

    fetchEvmBalance();
    fetchSolanaBalance();
  }, []);

  return (
    <div className="min-h-screen w-full flex flex-col items-center p-10">
      {evmBalanceData && <p>{JSON.stringify(evmBalanceData, null, 2)}</p>}
      {solanaBalanceData && <p>{JSON.stringify(solanaBalanceData, null, 2)}</p>}
    </div>
  );
}
```
