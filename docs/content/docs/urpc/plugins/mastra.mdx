---
title: Mastra Plugin
description: The Mastra Plugin integrates AI-powered natural language processing capabilities into your URPC applications, enabling users to interact with your data through conversational interfaces.
---

## Installation

```package-install
@unilab/mastra-plugin @unilab/mastra-client-plugin
```

## Basic Usage

### Server

```typescript
import { URPC } from "@unilab/urpc-hono";
import { MastraPlugin } from "@unilab/mastra-plugin/hono";
import { UserEntity } from "./entities/user";
import { UserAdapter } from "./adapters/user";

const MyPlugin = {
  entities: [UserEntity],
  adapters: [
    {
      entity: "UserEntity",
      source: "database",
      adapter: new UserAdapter(),
    },
  ],
};

const app = URPC.init({
  plugins: [
    MyPlugin,
    MastraPlugin({
      agents: {
        "l1": new URPCSimpleAgent({
          URPC,
          defaultModel: "google/gemini-2.0-flash-001",
          openrouterApiKey: process.env.OPENROUTER_API_KEY,
          debug: false,
        }),
      },
      defaultAgent: "l1",
    }),
  ],
});

export default {
  port: 3000,
  fetch: app.fetch,
};
```

### Client

```typescript
import { ChatEntity } from "@unilab/mastra-plugin/entities";
import { repo, URPC } from "@unilab/urpc";

URPC.init({
  plugins: [MastraClientPlugin()],

  baseUrl: "http://localhost:3000",
  timeout: 10000,
});

// Generate urpc code on the server side and execute it
const chatWithAI = async () => {
  const result = await repo<ChatEntity>({
    entity: "chat",
    source: "mastra",
  }).call({
    input: "Find all users with email containing 'example.com'",
    model: "google/gemini-2.0-flash-001",
    agent: "l1",
    entities: ["UserEntity"],
  });

  console.log(result.output);
};

// Generate urpc code on the server side, execute AI generated code on the front end
const chatWithAIProxy = async () => {
  const result = await repo<ChatEntity>({
    entity: "chat",
    source: "mastra-client",
  }).call({
    input: "Find all users with email containing 'example.com'",
    model: "google/gemini-2.0-flash-001",
    agent: "l1",
    entities: ["UserEntity"],
  });
  console.log(result.output);
};
```
