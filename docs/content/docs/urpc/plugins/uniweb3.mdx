---
title: UniWeb3 Plugin
description: UniWeb3 is a comprehensive blockchain plugin for URPC that provides unified access to multiple blockchain networks. It includes support for EVM-compatible chains and Solana, allowing you to query blockchain data through a consistent API.
---

## Installation

For Hono server:

```package-install
@unilab/urpc-hono @unilab/uniweb3
```

For Next.js:

```package-install
@unilab/urpc-next @unilab/urpc @unilab/urpc-core @unilab/uniweb3
```

Runs only in a browser or node environment:

```package-install
@unilab/urpc @unilab/urpc-core @unilab/uniweb3
```

## Server Setup

### Hono Server Setup

```typescript
import { URPC } from "@unilab/urpc-hono";
import { WalletPlugin } from "@unilab/uniweb3";
import { logging } from "@unilab/urpc-core/middleware";

const app = URPC.init({
  plugins: [WalletPlugin],
  middlewares: [logging()],
});

export default {
  port: 3000,
  fetch: app.fetch,
};
```

### Next.js Pages Router Setup

```typescript
// pages/api/[...urpc].ts
import { URPC } from "@unilab/urpc-next/pages-router";
import { WalletPlugin } from "@unilab/uniweb3";
import { logging } from "@unilab/urpc-core/middleware";

const handler = URPC.init({
  plugins: [WalletPlugin],
  middlewares: [logging()],
});

export default handler;
```

### Runs only in a browser

```typescript
import { URPC } from "@unilab/urpc";
import { WalletPlugin } from "@unilab/uniweb3";
import { logging } from "@unilab/urpc-core/middleware";

URPC.init({
  plugins: [WalletPlugin],
  middlewares: [logging()],
});
```

## Client Usage

### Initializing the Client

```typescript
import { repo, URPC } from "@unilab/urpc";
import { WalletEntity } from "@unilab/uniweb3/entities";

URPC.init({
  baseUrl: "http://localhost:3000",
  timeout: 10000,
});
```

### EVM Wallet Queries

```typescript
const evmWallet = await repo<WalletEntity>({
  entity: "wallet",
  source: "evm",
}).findOne({
  where: {
    address: "0x...",
    chainId: 1,
  },
});

console.log("EVM Wallet:", evmWallet);
```

### Solana Wallet Queries

```typescript
const solanaWallet = await repo<WalletEntity>({
  entity: "wallet",
  source: "solana",
}).findOne({
  where: {
    address: "********************************",
  },
});

console.log("Solana Wallet:", solanaWallet);
```

### You can also pass rpcUrl for queries

```typescript
const evmWallet = await repo<WalletEntity>({
  entity: "wallet",
  source: "evm",
}).findOne({
  where: {
    address: "0x...",
    rpcUrl: "https://eth.merkle.io",
  },
});
```

### Using Class Instances

Instead of passing entity name strings, you can directly pass entity classes, which will return corresponding class instances:

```typescript
import { WalletEntity } from "@unilab/uniweb3/entities";

// Pass class instead of string, returns class instance
const evmWallet = repo<WalletEntity>({
  entity: WalletEntity, // Pass class
  source: "evm",
}).findOne({
  where: {
    address: "0x...",
    chainId: 1,
  },
});

// evmWallet is now an instance of WalletEntity class
const client = evmWallet?.client;
if (client) {
  const blockNumber = await client.getBlockNumber();
  console.log("blockNumber:", blockNumber);
}

const connection = evmWallet?.connection;
if (connection) {
  const blockHeight = await connection.getBlockHeight();
  console.log("blockHeight:", blockHeight);
}
```
