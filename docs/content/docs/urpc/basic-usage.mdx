---
title: Basic Usage
description: Learn how to set up URPC with plugins and middleware
---

## Quick Start

Get started with URPC in seconds using our CLI tool:

<Tabs items={['npm', 'yarn', 'bun']}>
  <Tab>
    ```bash
    npx @unilab/urpc-cli create my-project
    ```
  </Tab>
  <Tab>
    ```bash
    yarn create @unilab/urpc-cli my-project
    ```
  </Tab>
  <Tab>
    ```bash
    bunx @unilab/urpc-cli create my-project
    ```
  </Tab>
</Tabs>

## Manual Setup

### Install Dependencies

```package-install
@unilab/urpc-hono @unilab/urpc @unilab/urpc-core @unilab/mastra-plugin
```

### Create Server

```typescript
import { URPC } from "@unilab/urpc-hono";
import { MastraPlugin } from "@unilab/mastra-plugin/hono";
import { logging } from "@unilab/urpc-core/middleware";
import { MockAdapter } from "@unilab/urpc-adapters";
import { UserEntity } from "./entities/user";

const MyPlugin = {
  entities: [UserEntity],
};

const app = URPC.init({
  plugins: [
    MyPlugin,
    MastraPlugin({
      openrouterApiKey: process.env.OPENROUTER_API_KEY,
    }),
  ],
  middlewares: [logging()],
  entityConfigs: {
    user: {
      defaultSource: "mock",
    },
  },
  globalAdapters: [MockAdapter],
});

export default {
  port: 3000,
  fetch: app.fetch,
};
```

### Use repo on the server side

```typescript
import { URPC } from "@unilab/urpc-hono";
import { UserEntity } from "./entities/user";

await URPC.repo<UserEntity>({
  entity: "user",
  source: "mock",
}).findOne({
  where: {
    id: "1",
  },
});
```

### Client Usage

```typescript
import { repo, URPC } from "@unilab/urpc";
import { ChatEntity } from "@unilab/mastra-plugin/entities";
import { UserEntity } from "./entities/user";

URPC.init({
  baseUrl: "http://localhost:3000",
  timeout: 10000,
});

// Traditional entity operations
const user = await repo<UserEntity>({
  entity: "user",
  source: "mock",
}).findOne({
  where: {
    id: "1",
  },
});

// AI-powered natural language queries
const aiResult = await repo<ChatEntity>({
  entity: "chat",
  source: "mastra",
}).call({
  input: "Find all users",
  model: "google/gemini-2.0-flash-001",
});

console.log(aiResult.output);
```
