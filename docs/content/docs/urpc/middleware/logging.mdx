---
title: Logging Middleware
description: Logging Middleware is a powerful middleware that automatically logs all repository operations including execution time, arguments, results, and error information. It provides detailed insights into your application's data access patterns and helps with debugging and monitoring.
---

## Installation

```package-install
@unilab/urpc-core
```

## Basic Usage

```typescript
import { logging } from "@unilab/urpc-core/middleware";

const app = URPC.init({
  middlewares: [logging()],
});
```

### Custom Logger Implementation

```typescript
import { logging } from "@unilab/urpc-core/middleware";

// Custom logger function
const customLogger = (message: string, context?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] CUSTOM: ${message}`, context);
};

const app = URPC.init({
  middlewares: [logging(customLogger)],
});
```

### Integration with External Logging Services

```typescript
import winston from "winston";

const logger = winston.createLogger({
  level: "info",
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: "error.log", level: "error" }),
    new winston.transports.File({ filename: "combined.log" }),
  ],
});

const winstonLogging = logging((message, context) => {
  if (context?.error) {
    logger.error(message, context);
  } else {
    logger.info(message, context);
  }
});

const app = URPC.init({
  middlewares: [winstonLogging],
});
```

### Conditional Logging

```typescript
const conditionalLogger = (message: string, context?: any) => {
  // Only log slow operations (> 1000ms)
  if (message.includes("completed") || message.includes("failed")) {
    const durationMatch = message.match(/(\d+)ms/);
    const duration = durationMatch ? parseInt(durationMatch[1]) : 0;

    if (duration > 1000) {
      console.warn(`SLOW OPERATION: ${message}`, context);
    }
  } else {
    console.log(message, context);
  }
};

const conditionalLogging = logging(conditionalLogger);
```

### Debug Mode

For development, you can create a debug-specific logger:

```typescript
const debugLogger = (message: string, context?: any) => {
  if (process.env.NODE_ENV === "development") {
    console.debug(`[DEBUG] ${message}`, context);
  }
};

const debugLogging = logging(debugLogger);
```
