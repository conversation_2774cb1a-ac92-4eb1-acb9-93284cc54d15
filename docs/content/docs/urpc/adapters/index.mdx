---
title: Global Adapters
description: A collection of adapters for URPC that provide different data storage solutions. Choose the right adapter for your development needs.
---

## Installation

```package-install
@unilab/urpc-adapters
```

## Available Adapters

### Memory Adapter
- **Use case**: Development, testing, temporary storage
- **Storage**: In-memory (lost on restart)
- **Speed**: Fastest access
- **Best for**: Development and caching

### Mock Adapter  
- **Use case**: Testing, prototyping
- **Storage**: In-memory (lost on restart)
- **Speed**: Fast
- **Best for**: Unit tests and demos

### IndexedDB Adapter
- **Use case**: Browser applications
- **Storage**: Persistent browser storage
- **Speed**: Good performance
- **Best for**: Client-side apps, PWAs, offline support
