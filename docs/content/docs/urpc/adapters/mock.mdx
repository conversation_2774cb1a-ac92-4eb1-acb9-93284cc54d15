---
title: Mock Adapter
description: Mock Adapter is a simple in-memory adapter for URPC that provides temporary data storage during application runtime. It's perfect for testing, prototyping, and development scenarios where you don't need persistent data storage.
---

### Basic Usage

```typescript
import { UserEntity } from "./entities/user";
import { repo, URPC } from "@unilab/urpc";
import { MockAdapter } from "@unilab/urpc-adapters";

const MyPlugin = {
  entities: [UserEntity],
};

URPC.init({
  plugins: [MyPlugin],
  entityConfigs: {
    user: {
      defaultSource: "mock",
    },
  },
  globalAdapters: [MockAdapter],
});
```

### Creating Data

```typescript
async function createUser() {
  await repo<UserEntity>({
    entity: "user",
  }).create({
    data: {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "https://example.com/avatar.png",
    },
  });
}
```

### Querying Data

```typescript
async function getUsers() {
  const users = await repo({
    entity: "user",
  }).findMany();

  console.log("All users:", users);
}
```
