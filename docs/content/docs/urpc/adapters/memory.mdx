---
title: Memory Adapter
description: Memory Adapter is a lightweight in-memory storage adapter for URPC that stores data directly in application memory. It's perfect for development, testing, and scenarios where you need temporary data storage without persistence.
---

### Basic Setup

```typescript
import { URPC } from "@unilab/urpc";
import { MemoryAdapter } from "@unilab/urpc-adapters";
import { UserEntity } from "./entities/user";

const MyPlugin = {
  entities: [UserEntity],
};

URPC.init({
  plugins: [MyPlugin],
  entityConfigs: {
    user: {
      defaultSource: "memory",
    },
  },
  globalAdapters: [MemoryAdapter],
});
```

### Creating Data

```typescript
import { repo } from "@unilab/urpc";

const newUser = await repo({
  entity: UserEntity,
  source: "memory",
}).create({
  data: {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
  },
});
console.log("Created user:", newUser);
```

### Querying Data

#### Using Entity Class (Returns Entity Instance)

```typescript
const userByClass = await repo({
  entity: UserEntity,
  source: "memory",
}).findOne({
  where: {
    id: "1",
  },
});

// Can call entity methods
userByClass?.click(" 123 ");
userByClass?.greet(" this is a test message ");
```

#### Using String Entity Name (Returns JSON Data)

```typescript
const userByString = await repo<UserEntity>({
  entity: "user", // Using string instead of class
  source: "memory",
}).findOne({
  where: {
    id: "1",
  },
});
console.log("User by string:", userByString);
```

### Querying Multiple Records

```typescript
const users = await repo<UserEntity>({
  entity: "user",
}).findMany({
  where: {
    id: "1",
  },
});
console.log("Found users:", JSON.stringify(users, null, 2));
```
