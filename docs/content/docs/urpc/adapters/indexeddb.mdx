---
title: IndexedDB Adapter
description: IndexedDB Adapter provides persistent browser storage for URPC entities using the IndexedDB API. Perfect for client-side applications that need data persistence across browser sessions.
---

### Basic Setup

```typescript
import { URPC } from "@unilab/urpc";
import { IndexedDBAdapter } from "@unilab/urpc-adapters";
import { UserEntity } from "./entities/user";

const MyPlugin = {
  entities: [UserEntity],
};

URPC.init({
  plugins: [MyPlugin],
  entityConfigs: {
    user: {
      defaultSource: "indexeddb",
    },
  },
  globalAdapters: [IndexedDBAdapter],
});
```

### Creating Data

```typescript
import { repo } from "@unilab/urpc";

const newUser = await repo({
  entity: UserEntity,
  source: "indexeddb",
}).create({
  data: {
    id: "user123",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://example.com/avatar.jpg",
  },
});
console.log("✅ User created:", newUser);
```

### Finding Data

```typescript
const user = await repo({
  entity: UserEntity,
  source: "indexeddb",
}).findOne({
  where: { id: "user123" },
});

if (user) {
  console.log("✅ User found:", user);
  // Call entity methods
  user.greet("Welcome to IndexedDB!");
} else {
  console.log("❌ User not found");
}
```

### Updating Data

```typescript
const updatedUser = await repo({
  entity: UserEntity,
  source: "indexeddb",
}).update({
  where: { id: "user123" },
  data: {
    name: "Jane Smith",
    email: "<EMAIL>",
  },
});
console.log("✅ User updated:", updatedUser);
```

### Deleting Data

```typescript
const deleted = await repo({
  entity: UserEntity,
  source: "indexeddb",
}).delete({
  where: { id: "user123" },
});

if (deleted) {
  console.log("✅ User deleted successfully");
} else {
  console.log("❌ User not found for deletion");
}
```

### Listing All Data

```typescript
const users = await repo({
  entity: UserEntity,
  source: "indexeddb",
}).findMany();

console.log(`Found ${users.length} users:`, users);
```

### Clearing All Data

```typescript
const allUsers = await repo({
  entity: UserEntity,
  source: "indexeddb",
}).findMany();

for (const user of allUsers) {
  await repo({
    entity: UserEntity,
    source: "indexeddb",
  }).delete({
    where: { id: user.id },
  });
}
console.log(`✅ Cleared ${allUsers.length} users`);
```

### Default Source Configuration

Set IndexedDB as the default source for entities:

```typescript
URPC.init({
  entityConfigs: {
    user: {
      defaultSource: "indexeddb",
    },
  },
  globalAdapters: [IndexedDBAdapter],
});

// Now you can omit the source parameter
const user = await repo({
  entity: UserEntity,
}).findOne({
  where: { id: "user123" },
});
```
