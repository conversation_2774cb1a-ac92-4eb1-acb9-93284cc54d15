---
title: Examples
description: These examples showcase the kind of apps you can build with URPC.
---

<DemoGrid>
  <DemoCard
    title="UniWeb3 Balance Query"
    description="Next.js application example using URPC and UniWeb3 for wallet operations"
    image="/urpc/examples/1.jpg"
    demoUrl="https://playground.uni-labs.org/uniweb3"
    sourceUrl="https://github.com/unify-procotol/playground"
  />
  
  <DemoCard
    title="Todo App Demo"
    description="Todo application example built with UniRender and URPC"
    image="/urpc/examples/2.jpg"
    demoUrl="https://playground.uni-labs.org/todo-app-demo"
    sourceUrl="https://github.com/unify-procotol/playground"
  />

  <DemoCard
    title="URPC + Mastra Plugin"
    description="Intelligent data operations assistant based on URPC + Mastra Plugin"
    image="/urpc/examples/3.jpg"
    demoUrl="https://playground.uni-labs.org/mastra-plugin"
    sourceUrl="https://github.com/unify-procotol/playground"
  />
</DemoGrid>
