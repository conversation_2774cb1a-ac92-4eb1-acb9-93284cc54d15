---
title: Card
description: Display data in visually rich card format with automatic URPC integration and customizable layouts
---

# Card

The card layout displays data in a visually appealing card format with automatic URPC integration, perfect for showcasing products, profiles, or any content that benefits from a visual presentation. Each record is displayed as an individual card with customizable content and actions.

## 🎯 Basic Card Layout

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="card" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

<UniRender
  entity="product"
  source="demo"
  layout="card"
  config={{
    name: { 
      label: 'Product Name',
      render: (value) => (
        <h3 className="text-lg font-semibold text-gray-900">{value}</h3>
      )
    },
    price: { 
      label: 'Price',
      render: (value) => (
        <span className="text-lg font-bold text-green-600">
          ${value.toFixed(2)}
        </span>
      )
    },
    category: {
      label: 'Category',
      render: (value) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
          {value}
        </span>
      )
    },
    inStock: { 
      label: 'Stock Status',
      render: (value) => (
        <span className={`px-2 py-1 rounded text-xs font-semibold ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'In Stock' : 'Out of Stock'}
        </span>
      )
    },
    description: {
      label: 'Description',
      render: (value) => (
        <p className="text-gray-600 text-sm line-clamp-3">{value}</p>
      )
    }
  }}
/>
```
</Tab>
</Tabs>

## 🔍 Filtered Card Layout

Display filtered products with query parameters:

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="card" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

<UniRender
  entity="product"
  source="demo"
  layout="card"
  query={{
    where: { 
      category: "electronics",
      inStock: true,
      price: { $lt: 500 }
    },
    orderBy: { price: "asc" },
    limit: 12
  }}
  config={{
    name: { 
      label: 'Product Name',
      render: (value) => (
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{value}</h3>
      )
    },
    price: { 
      label: 'Price',
      render: (value) => (
        <div className="text-xl font-bold text-green-600 mb-2">
          ${value.toFixed(2)}
        </div>
      )
    },
    category: {
      label: 'Category',
      render: (value) => (
        <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs mb-2">
          {value}
        </span>
      )
    },
    description: {
      label: 'Description',
      render: (value) => (
        <p className="text-gray-600 text-sm leading-relaxed">{value}</p>
      )
    }
  }}
  generalConfig={{
    showActions: true,
    actions: {
      custom: [
        {
          label: 'View Details',
          onClick: (record) => console.log('View:', record),
          className: 'bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600'
        },
        {
          label: 'Add to Cart',
          onClick: (record) => console.log('Add to cart:', record),
          className: 'bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600'
        }
      ]
    }
  }}
/>
```
</Tab>
</Tabs>

## 🎛️ Card Configuration

### Basic Field Setup

```tsx
const cardConfig = {
  // Primary information (larger text)
  name: {
    label: 'Product Name',
    order: 1
  },
  
  // Secondary information
  category: {
    label: 'Category',
    order: 2
  },
  
  // Price with custom formatting
  price: {
    label: 'Price',
    order: 3,
    align: 'right'
  },
  
  // Status indicators
  inStock: {
    label: 'Availability',
    order: 4
  },
  
  // Description (usually last)
  description: {
    label: 'Description',
    order: 5
  },
  
  // Hidden fields
  id: {
    hidden: true
  }
};
```

### Responsive Grid

Cards automatically arrange in a responsive grid:
- **Desktop**: 3-4 cards per row
- **Tablet**: 2 cards per row  
- **Mobile**: 1 card per row

## 🎨 Styling Options

### Card Spacing

```tsx
<UniRender
  layout="card"
  className="gap-6 p-4"
  // ... other props
/>
```

### Custom Card Styling

```css
.ukit-render .card-layout {
  /* Card container */
  gap: 1.5rem;
  padding: 1rem;
}

.ukit-render .card-item {
  /* Individual cards */
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.ukit-render .card-item:hover {
  /* Hover effects */
  transform: translateY(-2px);
  box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}
```

## 🔧 Advanced Features

### Image Support

While not directly supported in field configuration, you can add images through the entity data:

```tsx
const productData = [
  {
    id: 1,
    name: 'Wireless Headphones',
    image: '/images/headphones.jpg',
    price: 199.99,
    // ... other fields
  }
];

const config = {
  image: {
    label: 'Product Image',
    order: 0,
    width: '100%'
  }
};
```

### Action Buttons

Cards support the same action system as tables:

```tsx
const generalConfig = {
  showActions: true,
  actions: {
    edit: true,
    delete: true,
    custom: [
      {
        label: 'Add to Cart',
        icon: <ShoppingCart size={16} />,
        onClick: (product) => addToCart(product),
        className: 'bg-blue-600 text-white px-3 py-1 rounded'
      }
    ]
  }
};
```

## 📱 Mobile Optimization

Cards are inherently mobile-friendly:
- Touch-friendly interaction areas
- Readable text sizes
- Optimal spacing for thumb navigation
- Single-column layout on small screens

## 🌟 Best Practices

1. **Visual Hierarchy**: Put most important information first
2. **Consistent Sizing**: Ensure cards have uniform height when possible
3. **Clear Actions**: Make interactive elements obvious
4. **Loading States**: Show skeleton cards while loading
5. **Image Optimization**: Use appropriate image sizes and formats

## 🔗 Related

- [Table Layout](/docs/ukit/components/table-layout) - Structured data display
- [Grid Layout](/docs/ukit/components/grid) - Alternative visual layout
- [Form Layout](/docs/ukit/components/form) - Single item editing 