---
title: Custom Layout
description: Create completely custom layouts using render functions with automatic URPC integration and flexible design patterns
---

# Custom Layout

The custom layout allows you to create completely custom data visualizations using render functions, giving you full control over the layout, styling, and behavior while maintaining automatic URPC integration and built-in features like pagination and actions.

## 🎯 Basic Custom Layout

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="custom-basic" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

<UniRender
  entity="post"
  source="memory"
  layout="custom"
  render={renderCustomCardLayout}
  config={{
    name: { label: 'Title' },
    content: { label: 'Content' },
    role: { label: 'Role' },
    type: { label: 'Type' },
    category: { label: 'Category' },
    status: { label: 'Status' },
  }}
  pagination={{
    enabled: true,
    pageSize: 6,
  }}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```
</Tab>
</Tabs>

## 🎨 Magazine Style Layout

Create a clean blog-style layout perfect for articles and content display.

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="custom-magazine" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

// Magazine-style layout renderer
const renderMagazineLayout = (data: any[], options: any) => {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {data.map((record, index) => {
        const title = record.name || record.title || `Article ${index + 1}`;
        const content = record.content || record.description || "";
        const category = record.category || record.type || 'General';
        const author = record.email || record.author || 'Author';
        
        return (
          <article 
            key={record.id || index} 
            className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-200 overflow-hidden"
          >
            <div className="flex flex-col md:flex-row">
              {/* Thumbnail */}
              <div className="md:w-48 md:h-32 h-48 bg-gray-100 flex-shrink-0">
                {record.imageUrl ? (
                  <img src={record.imageUrl} alt={title} className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                      </svg>
                      <div className="text-xs text-gray-500">No image</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 p-5">
                {/* Category */}
                <div className="flex items-center mb-1">
                  <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {category}
                  </span>
                </div>

                {/* Title */}
                <h2 className="text-lg font-semibold text-gray-900 mb-3 mt-1 leading-tight hover:text-blue-600 cursor-pointer">
                  {title}
                </h2>

                {/* Content Preview */}
                {content && (
                  <p className="text-gray-600 mb-3 leading-relaxed line-clamp-2">
                    {content.length > 150 ? content.substring(0, 150) + "..." : content}
                  </p>
                )}

                {/* Meta Information */}
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <span>By {author}</span>
                    <span>•</span>
                    <span>Jan 15, 2024</span>
                    <span>•</span>
                    <span>5 min read</span>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <button className="hover:text-gray-700 transition-colors">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                      </svg>
                    </button>
                    <button className="hover:text-gray-700 transition-colors">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </article>
        );
      })}
    </div>
  );
};

<UniRender
  entity="post"
  source="memory"
  layout="custom"
  render={renderMagazineLayout}
  config={{
    name: { label: 'Article Title' },
    content: { label: 'Content' },
    role: { label: 'Department' },
    type: { label: 'Type' },
    category: { label: 'Category' },
    status: { label: 'Status' },
  }}
  pagination={{
    enabled: true,
    pageSize: 8,
  }}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```
</Tab>
</Tabs>

## 📱 Social Media Style Layout

Perfect for social media posts, user profiles, or any content that needs a social media feel.

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="custom-social" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

// Modern social media layout renderer
const renderSocialLayout = (data: any[], options: any) => {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {data.map((record, index) => {
        const title = record.name || record.title || `Post ${index + 1}`;
        const content = record.content || record.description || "";
        const author = record.email || record.author || 'User';
        const category = record.category || record.type || 'General';
        
        return (
          <div 
            key={record.id || index} 
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
          >
            {/* Header */}
            <div className="p-4 pb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-sm font-semibold text-white">
                    {author.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)}
                  </span>
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-sm">{author}</div>
                  <div className="text-xs text-gray-500">2h ago</div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="px-4 pb-3">
              <h3 className="font-semibold text-gray-900 mb-2">{title}</h3>
              {content && (
                <p className="text-gray-700 text-sm leading-relaxed mb-3">{content}</p>
              )}
              <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
                #{category.toLowerCase()}
              </span>
            </div>

            {/* Image placeholder */}
            <div className="aspect-[16/9] bg-gray-50 flex items-center justify-center">
              <div className="text-center">
                <svg className="w-12 h-12 text-gray-300 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                <div className="text-sm text-gray-400">No image</div>
              </div>
            </div>

            {/* Engagement & Actions */}
            <div className="px-4 py-3 border-t border-gray-100">
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                <span>156 likes</span>
                <span>23 comments</span>
                <span>5 shares</span>
              </div>
              <div className="flex items-center justify-between pt-2 border-t border-gray-50">
                <button className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span className="text-sm font-medium text-gray-700">Like</span>
                </button>
                <button className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span className="text-sm font-medium text-gray-700">Comment</span>
                </button>
                <button className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span className="text-sm font-medium text-gray-700">Share</span>
                </button>
                <button className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                  <span className="text-sm font-medium text-gray-700">Save</span>
                </button>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

<UniRender
  entity="post"
  source="memory"
  layout="custom"
  render={renderSocialLayout}
  config={{
    name: { label: 'Post Title' },
    content: { label: 'Content' },
    category: { label: 'Category' },
  }}
  pagination={{
    enabled: true,
    pageSize: 3,
  }}
/>
```
</Tab>
</Tabs>

## 📝 Blog Style Layout

Perfect for blog posts, articles, or any content-heavy applications. Features a clean, readable design similar to Medium or Dev.to.

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="custom-blog" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

// Modern blog layout renderer
const renderBlogLayout = (data: any[], options: any) => {
   return (
           <div className="max-w-4xl mx-auto px-6 py-8">
       <div className="space-y-6">
         {data.map((record, index) => {
           const title = record.name || record.title || `Article ${index + 1}`;
           const content = record.content || record.description || "";
           const category = record.category || record.type || 'General';
           const author = record.email || record.author || 'Author';
           const role = record.role || 'Writer';
           
           return (
             <article 
               key={record.id || index} 
               className="group bg-white border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200 rounded-lg p-6 shadow-sm hover:shadow-md"
             >
              <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
                {/* Content Section */}
                <div className="flex-1">
                  {/* Category & Reading Time */}
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors cursor-pointer">
                      {category}
                    </span>
                    <span className="text-gray-300">•</span>
                    <span className="text-sm text-gray-500">5 min read</span>
                  </div>

                  {/* Title */}
                  <h2 className="text-2xl font-bold text-gray-900 mb-3 leading-tight group-hover:text-blue-600 transition-colors duration-200 cursor-pointer">
                    {title}
                  </h2>

                  {/* Content Preview */}
                  {content && (
                    <p className="text-gray-600 mb-4 leading-relaxed text-base">
                      {content.length > 200 ? content.substring(0, 200) + "..." : content}
                    </p>
                  )}

                  {/* Author & Date */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-xs font-semibold text-white">
                          {author.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{author}</div>
                        <div className="text-xs text-gray-500">{role}</div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm text-gray-500">January 15, 2024</div>
                    </div>
                  </div>
                </div>

                {/* Image Section */}
                <div className="lg:w-48 lg:h-32 w-full h-48 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0 mt-4 lg:mt-0">
                  {record.imageUrl ? (
                    <img 
                      src={record.imageUrl} 
                      alt={title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="text-center">
                        <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <div className="text-xs text-gray-500">No image</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
                <div className="flex items-center space-x-6">
                  <button className="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors text-sm">
                    <span>24</span>
                  </button>
                  <button className="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors text-sm">
                    <span>8</span>
                  </button>
                  <button className="flex items-center space-x-2 text-gray-500 hover:text-purple-600 transition-colors text-sm">
                    <span>Save</span>
                  </button>
                </div>
                
                <button className="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                  Read more →
                </button>
              </div>
            </article>
          );
        })}
      </div>
    </div>
  );
};

<UniRender
  entity="post"
  source="memory"
  layout="custom"
  render={renderBlogLayout}
  config={{
    name: { label: 'Blog Title' },
    content: { label: 'Content' },
    role: { label: 'Author Role' },
    type: { label: 'Post Type' },
    category: { label: 'Category' },
    status: { label: 'Status' },
  }}
  pagination={{
    enabled: true,
    pageSize: 4,
  }}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```
</Tab>
</Tabs>

## 🔸 Minimal Layout

Clean and minimal design for simple data display. Perfect for content lists, news feeds, or any scenario where you need clean, distraction-free presentation.

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="custom-minimal" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

// Minimal layout renderer
const renderMinimalLayout = (data: any[], options: any) => {
  return (
    <div className="max-w-3xl mx-auto px-6 py-6">
      <div className="space-y-6">
        {data.map((record, index) => {
          const title = record.name || record.title || `Item ${index + 1}`;
          const content = record.content || record.description || "";
          const author = record.email || record.author || 'Author';
          
          return (
            <div 
              key={record.id || index} 
              className="pb-6 border-b border-gray-200 last:border-b-0"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-2 hover:text-blue-600 cursor-pointer line-clamp-2">
                {title}
              </h2>
              
              {content && (
                <p className="text-gray-600 mb-3 leading-relaxed line-clamp-3">
                  {content}
                </p>
              )}
              
              <div className="text-sm text-gray-500">
                {author}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

<UniRender
  entity="post"
  source="memory"
  layout="custom"
  render={renderMinimalLayout}
  config={{
    name: { label: 'Title' },
    content: { label: 'Description' },
    role: { label: 'Role' },
    status: { label: 'Status' },
  }}
  pagination={{
    enabled: true,
    pageSize: 8,
  }}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```
</Tab>
</Tabs>

## 🛠️ Props Reference

### UniRender Props for Custom Layout

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| `entity` | `string` | Entity name | Required |
| `source` | `string` | Data source | Required |
| `layout` | `'custom'` | Layout type | Required |
| `render` | `function` | Custom render function | Required |
| `config` | `object` | Field configuration | `{}` |
| `pagination` | `object` | Pagination settings | `{ enabled: false }` |
| `onEdit` | `function` | Edit handler | `undefined` |
| `onDelete` | `function` | Delete handler | `undefined` |
| `loading` | `boolean` | Loading state | `false` |
| `error` | `string \| null` | Error message | `null` |

## 🎯 Best Practices

### 1. **Responsive Design**
```tsx
<div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
  {/* Cards */}
</div>
```

### 2. **Performance Optimization**
```tsx
// Use React.memo for render function if it's expensive
const renderCustomLayout = React.memo((data: any[], options: any) => {
  // Your render logic
});
```

### 3. **Accessibility**
```tsx
<button
  onClick={() => options.onEdit(record, index)}
  aria-label={`Edit ${record.name}`}
  className="btn btn-primary"
>
  Edit
</button>
```

### 4. **Loading States**
```tsx
{options.deletingIndex === index ? (
  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
) : (
  'Delete'
)}
```

### 5. **Error Handling**
```tsx
const renderCustomLayout = (data: any[], options: any) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }
  
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {data.map((record, index) => (
        // Your card component
      ))}
    </div>
  );
};
```

## 🔗 Related Components

- [Table Layout](/docs/ukit/components/table-layout) - Structured data display
- [Card Layout](/docs/ukit/components/card-layout) - Built-in card layouts
- [Grid Layout](/docs/ukit/components/grid) - Grid-based layouts
- [List Layout](/docs/ukit/components/list) - List-based layouts

## 💡 Tips

1. **Use consistent styling** across your custom layouts
2. **Handle edge cases** like empty data or missing fields
3. **Add hover effects** to improve user interaction
4. **Consider mobile responsiveness** in your designs
5. **Use semantic HTML** for better accessibility
6. **Test with different data shapes** to ensure robustness 