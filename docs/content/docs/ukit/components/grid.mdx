---
title: Grid
description: Display data in responsive grid layout with automatic URPC integration, perfect for galleries and visual catalogs
---

# Grid

The grid layout displays data in a responsive grid format with automatic URPC integration, ideal for image galleries, product catalogs, and any content that benefits from a structured visual arrangement. Items are arranged in a flexible grid that adapts to different screen sizes.

## 🎯 Basic Grid Layout

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="grid" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

<UniRender
  entity="gallery"
  source="demo"
  layout="grid"
  config={{
    title: { 
      label: 'Title',
      render: (value) => (
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{value}</h3>
      )
    },
    image: {
      label: 'Image',
      render: (value, record) => (
        <img 
          src={value || '/placeholder-image.jpg'} 
          alt={record.title}
          className="w-full h-48 object-cover rounded-lg mb-3"
        />
      )
    },
    category: { 
      label: 'Category',
      render: (value) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
          {value}
        </span>
      )
    },
    views: { 
      label: 'Views',
      render: (value) => (
        <span className="text-gray-500 text-sm">
          {value.toLocaleString()} views
        </span>
      )
    },
    likes: { 
      label: 'Likes',
      render: (value) => (
        <span className="text-red-500 text-sm font-medium">
          {value} ❤️
        </span>
      )
    }
  }}
/>
```
</Tab>
</Tabs>

## 🔍 Filtered Grid Layout

Display filtered gallery items with query parameters:

<Tabs items={['Preview', 'Code']}>
<Tab value="Preview">
<UniRenderExample type="grid" />
</Tab>
<Tab value="Code">
```tsx
import { UniRender } from '@unilab/ukit';

<UniRender
  entity="gallery"
  source="demo"
  layout="grid"
  query={{
    where: { 
      category: "nature",
      published: true,
      likes: { $gte: 50 }
    },
    orderBy: { likes: "desc" },
    limit: 20
  }}
  config={{
    title: { 
      label: 'Title',
      render: (value) => (
        <h3 className="text-base font-semibold text-gray-900 line-clamp-2">{value}</h3>
      )
    },
    category: { 
      label: 'Category',
      render: (value) => (
        <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs mb-2">
          {value}
        </span>
      )
    },
    views: { 
      label: 'Views',
      render: (value) => (
        <div className="flex items-center text-gray-500 text-sm">
          <span className="mr-1">👁️</span>
          {value.toLocaleString()}
        </div>
      )
    },
    likes: { 
      label: 'Likes',
      render: (value) => (
        <div className="flex items-center text-red-500 text-sm font-medium">
          <span className="mr-1">❤️</span>
          {value}
        </div>
      )
    }
  }}
  generalConfig={{
    showActions: true,
    actions: {
      custom: [
        {
          label: 'View Full',
          onClick: (record) => window.open(record.image, '_blank'),
          className: 'bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600'
        },
        {
          label: 'Like',
          onClick: (record) => console.log('Liked:', record),
          className: 'bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600'
        }
      ]
    }
  }}
/>
```
</Tab>
</Tabs>

## 🎛️ Grid Configuration

### Basic Setup

```tsx
const gridConfig = {
  // Main image/content
  image: {
    label: 'Image',
    order: 1,
    width: '100%'
  },
  
  // Title (prominently displayed)
  title: {
    label: 'Title',
    order: 2
  },
  
  // Category tag
  category: {
    label: 'Category',
    order: 3
  },
  
  // Statistics
  views: {
    label: 'Views',
    order: 4,
    align: 'center'
  },
  
  likes: {
    label: 'Likes',
    order: 5,
    align: 'center'
  },
  
  // Hidden fields
  id: {
    hidden: true
  }
};
```

### Responsive Grid Options

```tsx
const gridSettings = {
  // Responsive columns
  columns: {
    sm: 1,  // Mobile: 1 column
    md: 2,  // Tablet: 2 columns
    lg: 3,  // Desktop: 3 columns
    xl: 4   // Large screens: 4 columns
  },
  
  // Spacing between items
  gap: '1rem',
  
  // Aspect ratio for items
  aspectRatio: '16:9',
  
  // Masonry layout (Pinterest-style)
  masonry: true
};
```

## 🖼️ Grid Variations

### Fixed Grid

Equal-sized items in a regular grid:

```tsx
<UniRender
  layout="grid"
  data={data}
  config={{
    ...gridConfig,
    aspectRatio: '1:1',
    masonry: false
  }}
/>
```

### Masonry Grid

Variable height items that flow naturally:

```tsx
<UniRender
  layout="grid"
  data={data}
  config={{
    ...gridConfig,
    masonry: true,
    minItemWidth: '280px'
  }}
/>
```

### Image Gallery

Optimized for photos and visual content:

```tsx
const galleryConfig = {
  image: {
    label: 'Photo',
    order: 1,
    width: '100%',
    aspectRatio: '4:3'
  },
  
  title: {
    label: 'Title',
    order: 2,
    overlay: true // Display over image
  },
  
  metadata: {
    label: 'Info',
    order: 3,
    overlay: true
  }
};
```

## 🎨 Styling Options

### Hover Effects

```css
.ukit-render .grid-layout .grid-item {
  transition: transform 0.3s ease;
}

.ukit-render .grid-layout .grid-item:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
```

### Custom Spacing

```tsx
<UniRender
  layout="grid"
  className="grid-spacing-lg"
  data={data}
  config={gridConfig}
/>
```

```css
.grid-spacing-lg .grid-layout {
  gap: 2rem;
  padding: 2rem;
}
```

## 🔧 Advanced Features

### Overlay Information

Display information over images:

```tsx
const overlayConfig = {
  image: {
    label: 'Image',
    order: 1,
    overlay: {
      title: 'title',
      subtitle: 'category',
      metadata: ['views', 'likes']
    }
  }
};
```

### Action Buttons

Add action buttons to grid items:

```tsx
const gridActions = {
  showActions: true,
  actions: {
    custom: [
      {
        label: 'View',
        icon: <Eye size={16} />,
        onClick: (item) => viewItem(item),
        className: 'bg-blue-600 text-white'
      },
      {
        label: 'Like',
        icon: <Heart size={16} />,
        onClick: (item) => likeItem(item),
        className: 'bg-red-600 text-white'
      }
    ]
  }
};
```

### Infinite Scroll

Load more items as user scrolls:

```tsx
const [items, setItems] = useState(initialItems);
const [loading, setLoading] = useState(false);

const loadMore = async () => {
  setLoading(true);
  const newItems = await fetchMoreItems();
  setItems([...items, ...newItems]);
  setLoading(false);
};

<UniRender
  layout="grid"
  data={items}
  config={gridConfig}
  onLoadMore={loadMore}
  loading={loading}
/>
```

## 📱 Mobile Optimization

Grid layout is optimized for mobile:

- Touch-friendly item sizes
- Smooth scrolling performance
- Adaptive column counts
- Optimized image loading

### Mobile-Specific Settings

```tsx
const mobileGridConfig = {
  columns: {
    sm: 1,
    md: 2
  },
  gap: '0.75rem',
  touchOptimized: true,
  lazyLoading: true
};
```

## 🎯 Best Practices

### Performance Tips

1. **Image Optimization**: Use optimized image formats (WebP, AVIF)
2. **Lazy Loading**: Load images only when needed
3. **Virtual Scrolling**: For large datasets (1000+ items)
4. **Caching**: Cache frequently accessed items

### Layout Guidelines

1. **Consistent Sizing**: Use aspect ratios for uniform appearance
2. **Adequate Spacing**: Ensure items don't feel cramped
3. **Clear Hierarchy**: Use typography to establish information hierarchy
4. **Accessibility**: Include alt text and keyboard navigation

### Grid vs Other Layouts

**Use Grid when:**
- Visual content is primary
- Items have images or rich media
- Browsing and discovery are key
- Space efficiency is important

**Use Table when:**
- Data comparison is needed
- Structured information is primary
- Sorting and filtering are required

**Use Cards when:**
- Detailed information per item
- Actions are important
- Sequential browsing is preferred

## 🔗 Related Components

- [Card Layout](/docs/ukit/components/card-layout) - For detailed item displays
- [Table Layout](/docs/ukit/components/table-layout) - For structured data
- [List Layout](/docs/ukit/components/list) - For text-heavy content 