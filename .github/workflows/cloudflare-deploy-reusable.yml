name: Reusable Cloudflare Deployment

on:
  workflow_call:
    inputs:
      working-directory:
        required: true
        type: string
        description: 'The directory containing the project to deploy'
      deployment-type:
        required: false
        type: string
        default: 'workers'
        description: 'Deployment type: workers or pages'
      project-name:
        required: false
        type: string
        description: 'Cloudflare Pages project name (only needed for Pages deployments)'
      branch-name:
        required: false
        type: string
        description: 'Cloudflare Pages branch name (only needed for Pages deployments)'
      build-command:
        required: false
        type: string
        default: 'bun run pages:build'
        description: 'Command to build the project'
    secrets:
      CLOUDFLARE_API_TOKEN:
        required: true
      CLOUDFLARE_ACCOUNT_ID:
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ inputs.working-directory }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js and Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
          
      - name: Install dependencies
        run: bun install

      - name: Build project
        run: ${{ inputs.build-command }}

      - name: Deploy to Cloudflare Workers with OpenNext
        if: ${{ inputs.deployment-type == 'workers' }}
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: bun run deploy

      - name: Deploy to Cloudflare Pages
        if: ${{ inputs.deployment-type == 'pages' }}
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy --project-name=${{ inputs.project-name }} --branch=${{ inputs.branch-name }}
          workingDirectory: ${{ inputs.working-directory }}
          packageManager: bun 
          wranglerVersion: "4.25.1"