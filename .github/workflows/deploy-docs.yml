name: Deploy Docs to Cloudflare Pages

on:
  push:
    branches: [main]
    paths:
      - 'docs/**'
      - '.github/workflows/deploy-docs.yml'
  workflow_dispatch:

jobs:
  deploy-docs:
    uses: ./.github/workflows/cloudflare-deploy-reusable.yml
    with:
      working-directory: ./docs
      deployment-type: pages
      project-name: unify-docs
      branch-name: unify-docs
      build-command: 'bun run pages:build'
    secrets:
      CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }} 