name: Deploy AI Chat Example to Cloudflare Workers

on:
  push:
    branches: [main]
    paths:
      - 'examples/ai-chat-example/**'
      - '.github/workflows/deploy-ai-chat-example.yml'
  workflow_dispatch:

jobs:
  deploy-ai-chat:
    uses: ./.github/workflows/cloudflare-deploy-reusable.yml
    with:
      working-directory: ./examples/ai-chat-example
      deployment-type: workers
      build-command: 'bun run pages:build'
    secrets:
      CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }} 